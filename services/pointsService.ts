import { supabase } from '@/lib/supabase';
import { CustomerPointsSummary, ServiceResponse } from './types';

/**
 * Points service for handling customer points-related operations
 */
export class PointsService {
  /**
   * Get customer points summary including total earned, active, and redeemed points
   * @param customerId - The customer's UUID
   * @returns Promise with points summary data or error
   */
  static async getCustomerPointsSummary(
    customerId: string
  ): Promise<ServiceResponse<CustomerPointsSummary>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_customer_points_summary',
        {
          p_customer_id: customerId,
        }
      );

      if (error) {
        console.error('Error fetching customer points summary:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch points summary',
        };
      }

      // The function returns an array with a single object
      const summary = data?.[0];

      if (!summary) {
        return {
          data: null,
          error: 'No points data found for customer',
        };
      }

      return {
        data: {
          total_earned: summary.total_earned || 0,
          total_active: summary.total_active || 0,
          total_redeemed: summary.total_redeemed || 0,
        },
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in getCustomerPointsSummary:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }
}
