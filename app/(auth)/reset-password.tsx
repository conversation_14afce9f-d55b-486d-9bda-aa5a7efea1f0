import React from 'react';
import { router } from 'expo-router';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/button';
import { Input, InputField } from '@/components/ui/input';
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
} from '@/components/ui/form-control';
import { useAuthForm } from '@/hooks/useAuthForm';

export default function ResetPassword() {
  const { formState, errors, loading, updateField, handleSubmit } =
    useAuthForm('reset');

  const onSubmit = async () => {
    await handleSubmit();
    if (!errors.general) {
      router.replace('/(auth)/sign-in');
    }
  };

  return (
    <Box className='flex-1 bg-background-0 justify-center px-6'>
      <VStack space='xl' className='w-full max-w-md mx-auto'>
        <VStack space='md' className='items-center'>
          <Heading size='2xl' className='text-typography-900'>
            Create New Password
          </Heading>
          <Text size='md' className='text-typography-500 text-center'>
            Enter your new password below
          </Text>
        </VStack>

        <VStack space='lg'>
          <FormControl isInvalid={!!errors.password}>
            <FormControlLabel>
              <FormControlLabelText>New Password</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                type='password'
                placeholder='Enter your new password'
                value={formState.password}
                onChangeText={text => updateField('password', text)}
                secureTextEntry
                autoComplete='new-password'
              />
            </Input>
            <FormControlError>
              <FormControlErrorText>{errors.password}</FormControlErrorText>
            </FormControlError>
          </FormControl>

          <FormControl isInvalid={!!errors.confirmPassword}>
            <FormControlLabel>
              <FormControlLabelText>Confirm New Password</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                type='password'
                placeholder='Confirm your new password'
                value={formState.confirmPassword}
                onChangeText={text => updateField('confirmPassword', text)}
                secureTextEntry
                autoComplete='new-password'
              />
            </Input>
            <FormControlError>
              <FormControlErrorText>
                {errors.confirmPassword}
              </FormControlErrorText>
            </FormControlError>
          </FormControl>

          {errors.general && (
            <Text size='sm' className='text-error-500 text-center'>
              {errors.general}
            </Text>
          )}

          <Button onPress={onSubmit} isDisabled={loading} className='w-full'>
            {loading && <ButtonSpinner />}
            <ButtonText>
              {loading ? 'Updating Password...' : 'Update Password'}
            </ButtonText>
          </Button>
        </VStack>
      </VStack>
    </Box>
  );
}
