import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Spinner } from '@/components/ui/spinner';
import { useAuth } from '@/contexts/AuthContext';
import { PointsService, CustomerPointsSummary } from '@/services';

export default function Home() {
  const { user } = useAuth();
  const [pointsData, setPointsData] = useState<CustomerPointsSummary | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Extract name from email (everything before @)
  const getUserName = () => {
    if (!user?.email) return 'User';
    const emailParts = user.email.split('@');
    const namePart = emailParts[0];
    // Convert to proper case (first letter uppercase, rest lowercase)
    return namePart.charAt(0).toUpperCase() + namePart.slice(1).toLowerCase();
  };

  // Fetch points data when component mounts or user changes
  useEffect(() => {
    const fetchPointsData = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      const result = await PointsService.getCustomerPointsSummary(user.id);

      if (result.error) {
        setError(result.error);
        setPointsData(null);
      } else {
        setPointsData(result.data);
        setError(null);
      }

      setLoading(false);
    };

    fetchPointsData();
  }, [user?.id]);

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Home
          </Heading>

          {/* Colored divider line */}
          <HStack space='xs' className='w-full'>
            <Box className='flex-1 h-1 bg-primary-500 rounded-full' />
            <Box className='flex-1 h-1 bg-secondary-500 rounded-full' />
            <Box className='flex-1 h-1 bg-error-500 rounded-full' />
          </HStack>

          {/* Greeting */}
          <VStack space='xs' className='items-center'>
            <Heading
              size='xl'
              className='text-typography-900 font-semibold text-center'
            >
              Good afternoon, {getUserName()}
            </Heading>
            <Heading
              size='xl'
              className='text-typography-900 font-semibold text-center'
            >
              Indie Points!
            </Heading>
            <Text size='md' className='text-typography-600 text-center'>
              Here&apos;s your points summary.
            </Text>
          </VStack>
        </VStack>

        <Box className='px-6 pb-8'>
          {loading ? (
            // Loading state
            <VStack space='lg' className='items-center py-8'>
              <Spinner size='large' />
              <Text size='md' className='text-typography-600'>
                Loading your points...
              </Text>
            </VStack>
          ) : error ? (
            // Error state
            <VStack space='lg' className='items-center py-8'>
              <Text
                size='lg'
                className='text-error-500 font-semibold text-center'
              >
                Unable to load points data
              </Text>
              <Text size='md' className='text-typography-600 text-center'>
                {error}
              </Text>
              <Text size='sm' className='text-typography-500 text-center'>
                Please try again later or contact support if the problem
                persists.
              </Text>
            </VStack>
          ) : (
            // Data loaded successfully
            <VStack space='lg'>
              {/* Active Points Card */}
              <Box className='bg-primary-500 rounded-2xl border-4 border-primary-700 shadow-lg p-6'>
                <VStack space='xs'>
                  <Text size='lg' className='text-white font-medium'>
                    Active points
                  </Text>
                  <Heading size='4xl' className='text-white font-bold'>
                    {pointsData?.total_active?.toLocaleString() || '0'}
                  </Heading>
                </VStack>
              </Box>

              {/* Total Earned and Redeemed Cards */}
              <HStack space='md'>
                {/* Total Earned Card */}
                <Box className='flex-1 bg-secondary-500 rounded-2xl border-4 border-secondary-700 shadow-lg p-4'>
                  <VStack space='xs'>
                    <Text size='md' className='text-white font-medium'>
                      Total earned
                    </Text>
                    <Heading size='2xl' className='text-white font-bold'>
                      {pointsData?.total_earned?.toLocaleString() || '0'}
                    </Heading>
                  </VStack>
                </Box>

                {/* Total Redeemed Card */}
                <Box className='flex-1 bg-error-500 rounded-2xl border-4 border-error-700 shadow-lg p-4'>
                  <VStack space='xs'>
                    <Text size='md' className='text-white font-medium'>
                      Total redeemed
                    </Text>
                    <Heading size='2xl' className='text-white font-bold'>
                      {pointsData?.total_redeemed?.toLocaleString() || '0'}
                    </Heading>
                  </VStack>
                </Box>
              </HStack>
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
