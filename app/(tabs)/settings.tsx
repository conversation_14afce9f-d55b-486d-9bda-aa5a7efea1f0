import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>View } from 'react-native';
import { router } from 'expo-router';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import FontAwesome from '@expo/vector-icons/FontAwesome';

export default function Settings() {
  const { user, signOut } = useAuth();

  const handleSignOut = async () => {
    Alert.alert('Sign Out', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Sign Out',
        style: 'destructive',
        onPress: async () => {
          const { error } = await signOut();
          if (!error) {
            router.replace('/(auth)/sign-in');
          }
        },
      },
    ]);
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Settings
          </Heading>

          {/* Colored divider line */}
          <HStack space='xs' className='w-full'>
            <Box className='flex-1 h-1 bg-primary-500 rounded-full' />
            <Box className='flex-1 h-1 bg-secondary-500 rounded-full' />
            <Box className='flex-1 h-1 bg-error-500 rounded-full' />
          </HStack>
        </VStack>

        <Box className='flex-1 px-6 pb-8'>
          <VStack space='xl' className='flex-1'>
            {/* User Profile Card */}
            <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
              <HStack space='md' className='items-center'>
                {/* Profile Icon */}
                <Box className='w-16 h-16 bg-primary-500 rounded-2xl items-center justify-center'>
                  <FontAwesome name='user' size={24} color='white' />
                </Box>

                {/* User Info */}
                <VStack className='flex-1'>
                  <Text size='lg' className='text-typography-900 font-semibold'>
                    {user?.email || 'Not signed in'}
                  </Text>
                  <Text size='sm' className='text-typography-600'>
                    Member since 11 June 2025
                  </Text>
                </VStack>
              </HStack>
            </Box>

            {/* Sign Out Button */}
            <Button
              size='lg'
              onPress={handleSignOut}
              className='w-full bg-error-500 rounded-xl border-2 border-error-700 shadow-lg'
            >
              <ButtonText className='text-white font-semibold text-lg'>
                Sign Out
              </ButtonText>
            </Button>
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
