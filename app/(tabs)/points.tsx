import React from 'react';
import { ScrollView } from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import FontAwesome from '@expo/vector-icons/FontAwesome';

export default function Points() {
  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Points
          </Heading>

          {/* Colored divider line */}
          <HStack space='xs' className='w-full'>
            <Box className='flex-1 h-1 bg-primary-500 rounded-full' />
            <Box className='flex-1 h-1 bg-secondary-500 rounded-full' />
            <Box className='flex-1 h-1 bg-error-500 rounded-full' />
          </HStack>
        </VStack>

        <Box className='px-6 pb-8'>
          <VStack space='xl'>
            {/* Your loyalty card section */}
            <VStack space='lg' className='items-center'>
              <Heading size='xl' className='text-typography-900 font-semibold'>
                Your loyalty card
              </Heading>

              {/* QR Code placeholder */}
              <Box className='w-64 h-64 bg-white rounded-2xl border-4 border-typography-900 items-center justify-center shadow-lg'>
                <VStack space='md' className='items-center'>
                  <FontAwesome name='qrcode' size={120} color='#000' />
                  <Text
                    size='sm'
                    className='text-typography-600 text-center px-4'
                  >
                    Show this QR code at participating businesses
                  </Text>
                </VStack>
              </Box>

              {/* Action Buttons */}
              <VStack space='md' className='w-full max-w-xs'>
                <Button
                  size='lg'
                  className='w-full bg-primary-500 rounded-xl border-2 border-primary-700 shadow-lg'
                >
                  <HStack space='sm' className='items-center'>
                    <FontAwesome name='refresh' size={16} color='white' />
                    <ButtonText className='text-white font-semibold'>
                      Generate new QR code
                    </ButtonText>
                  </HStack>
                </Button>

                <Button
                  size='lg'
                  className='w-full bg-secondary-500 rounded-xl border-2 border-secondary-700 shadow-lg'
                >
                  <HStack space='sm' className='items-center'>
                    <FontAwesome name='qrcode' size={16} color='white' />
                    <ButtonText className='text-white font-semibold'>
                      Scan business QR code
                    </ButtonText>
                  </HStack>
                </Button>
              </VStack>
            </VStack>

            {/* How to Earn Points Section */}
            <VStack space='lg'>
              <Heading size='xl' className='text-typography-900 font-semibold'>
                How to Earn Points
              </Heading>

              {/* Step 1 */}
              <HStack space='md' className='items-start'>
                <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center'>
                  <Text size='md' className='text-white font-bold'>
                    1
                  </Text>
                </Box>
                <VStack className='flex-1'>
                  <Text size='md' className='text-typography-900 font-semibold'>
                    Visit a participating business
                  </Text>
                  <Text size='sm' className='text-typography-600'>
                    Look for the Indie Points logo at local businesses
                  </Text>
                </VStack>
              </HStack>

              {/* Step 2 */}
              <HStack space='md' className='items-start'>
                <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center'>
                  <Text size='md' className='text-white font-bold'>
                    2
                  </Text>
                </Box>
                <VStack className='flex-1'>
                  <Text size='md' className='text-typography-900 font-semibold'>
                    Show your QR code
                  </Text>
                  <Text size='sm' className='text-typography-600'>
                    Let the business scan your unique QR code before or after
                    purchase
                  </Text>
                </VStack>
              </HStack>

              {/* Step 3 */}
              <HStack space='md' className='items-start'>
                <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center'>
                  <Text size='md' className='text-white font-bold'>
                    3
                  </Text>
                </Box>
                <VStack className='flex-1'>
                  <Text size='md' className='text-typography-900 font-semibold'>
                    Earn points automatically
                  </Text>
                  <Text size='sm' className='text-typography-600'>
                    Get 1 point for every £1 spent at participating businesses
                  </Text>
                </VStack>
              </HStack>
            </VStack>
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
